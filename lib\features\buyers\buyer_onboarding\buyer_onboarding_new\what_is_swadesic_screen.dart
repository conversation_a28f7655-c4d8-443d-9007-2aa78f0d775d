import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/initial_onboarding_screen.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/what_is_swadesic_bloc.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_text_style.dart';

class WhatIsSwadesicScreen extends StatefulWidget {
  final String userReference;
  final Map<String, dynamic> userData;
  final String? icon;

  const WhatIsSwadesicScreen({
    Key? key,
    required this.userReference,
    required this.userData,
    this.icon,
  }) : super(key: key);

  @override
  _WhatIsSwadesicScreenState createState() => _WhatIsSwadesicScreenState();
}

class _WhatIsSwadesicScreenState extends State<WhatIsSwadesicScreen> {
  //region Bloc
  late WhatIsSwadesicBloc whatIsSwadesicBloc;

  //endregion

  //region Init
  @override
  void initState() {
    whatIsSwadesicBloc = WhatIsSwadesicBloc(context);
    super.initState();
  }

  //endregion

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.appWhite,
      body: SafeArea(
        child: body(),
      ),
    );
  }

  //region Body
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 40),
            header(),
            const SizedBox(height: 30),
            platformDescription(),
            const SizedBox(height: 40),
            swadesicMission(),
            const SizedBox(height: 100),
            areYouIn(),
            const SizedBox(height: 15),
            imInButton(),
            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }
  //endregion

  //region Header
  Widget header() {
    return Row(
      children: [
        Image.asset(
          AppImages.appIcon,
          height: 50,
          width: 50,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            "Swadesic is a Community on Mission",
            style: AppTextStyle.exHeading1(textColor: AppColors.appBlack),
          ),
        ),
      ],
    );
  }
  //endregion

  //region Platform Description
  Widget platformDescription() {
    return Text(
      "We are Bharat's first Direct-from-Store Shopping Network. Your every action is designed to support small business owners & consumers of the nation.",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }
  //endregion



  //region Swadesic Mission
  Widget swadesicMission() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                "What is Swadesic Mission",
                style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
              ),
            ),
            const Icon(
              Icons.keyboard_arrow_up,
              color: AppColors.appBlack,
              size: 24,
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          "To ensure national markets stay sovereign from foreign control — by restoring ownership to those who live, build, and buy within the nations.",
          style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
        ),
      ],
    );
  }
  //endregion

  //region Are You In
  Widget areYouIn() {
    return Text(
      "Are you in?",
      style: AppTextStyle.contentHeading0(textColor: AppColors.appBlack),
    );
  }
  //endregion

  //region I'm In Button
  Widget imInButton() {
    return SizedBox(
      width: double.infinity,
      child: CupertinoButton(
        borderRadius: BorderRadius.circular(11),
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        color: AppColors.appBlack,
        child: Text(
          "Yes, I am.",
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppTextStyle.access0(textColor: AppColors.appWhite),
        ),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => InitialOnboardingScreen(
                userReference: widget.userReference,
                icon: widget.icon,
              ),
            ),
          );
        },
      ),
    );
  }
  //endregion
}
