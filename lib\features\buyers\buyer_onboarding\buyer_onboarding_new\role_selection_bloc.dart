import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/services/upload_file_service.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

class RoleSelectionBloc {
  // region Common Methods
  BuildContext context;
  final String userReference;
  final ImagePicker picker = ImagePicker();
  final ImageCropper imageCrop = ImageCropper();
  String? userProfilePicturePath;
  List<String> selectedRoles = [];
  late UserDetailsServices userDetailsServices;

  // endregion

  //region Controller
  final userProfilePictureCtrl = StreamController<String>.broadcast();
  final loadingCtrl = StreamController<bool>.broadcast();
  final TextEditingController inviteCodeTextCtrl = TextEditingController();

  //endregion

  // region | Constructor |
  RoleSelectionBloc(this.context, this.userReference);

  // endregion

  // region Init
  void init() {
    userDetailsServices = UserDetailsServices();
  }
  // endregion

  //region Open Gallery
  void openGallery() async {
    try {
      XFile? galleryImage = await picker.pickImage(source: ImageSource.gallery);
      if (galleryImage == null) return;
      
      // Crop image
      crop(file: File(galleryImage.path));
    } catch (e) {
      debugPrint("Error selecting image: $e");
    }
  }
  //endregion

  //region Image crop
  crop({required File file}) async {
    File? croppedFile = await CommonMethods.imageCrop(file: File(file.path));
    
    if (croppedFile != null) {
      userProfilePicturePath = croppedFile.path;
      userProfilePictureCtrl.sink.add(croppedFile.path);
    }
  }
  //endregion

  //region Validate Roles
  bool validateRoles() {
    if (selectedRoles.isEmpty) {
      CommonMethods.toastMessage(AppStrings.pleaseSelectAtLeaseOneRole, context);
      return false;
    }
    return true;
  }
  //endregion

  //region Continue To Home
  void continueToHome({required Map<String, dynamic> userData}) async {
    loadingCtrl.sink.add(true);

    try {
      // Create user profile
      await createUserProfile(userData: userData);

      // Upload profile picture if available
      if (userProfilePicturePath != null) {
        await uploadProfilePicture();
      }

      // Get user details
      await getLoggedInUserDetail();

      // Navigate to home
      goToBottomNavigation();
    } catch (error) {
      loadingCtrl.sink.add(false);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    }
  }
  //endregion

  //region Create User Profile
  Future<void> createUserProfile({required Map<String, dynamic> userData}) async {
    try {
      // Update user info api
      await userDetailsServices.editUserProfile(
          data: userData, userReference: userReference);
    } on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
      rethrow;
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      rethrow;
    }
  }
  //endregion

  //region Upload Profile Picture
  Future<void> uploadProfilePicture() async {
    try {
      if (userProfilePicturePath != null) {
        final fileNameWithExtension = userProfilePicturePath!.split('/').last;
        await UploadFileService().addUserProfilePic(
          fileNameWithExtension: fileNameWithExtension,
          filePath: userProfilePicturePath!,
        );
      }
    } on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
      rethrow;
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      rethrow;
    }
  }
  //endregion

  //region Get logged in user detail
  Future<void> getLoggedInUserDetail() async {
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);

    late GetUserDetailsResponse userDetailsResponse;

    try {
      userDetailsResponse = await UserDetailsServices()
          .getLoggedInUserDetail(userReference: userReference);

      ///Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(
          data: userDetailsResponse.userDetail!);

      //Update the buy button to refresh in all loaded product
      for (var product in productDataModel.allProducts) {
        product.isPinCodeChanged = true;
      }

      //Update ui
      productDataModel.updateUi();

      ///Save user info to global and share pref
      saveUserInfoInGlobalAndSharePref();
    } on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message.toString(), context);
      }
      rethrow;
    } catch (error) {
      rethrow;
    }
  }
  //endregion

  //region Save user info in global and share pref
  void saveUserInfoInGlobalAndSharePref() {
    AppDataService().addAppData();
  }
  //endregion

  //region Go to Bottom Navigation
  void goToBottomNavigation() {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const UserBottomNavigation()),
      (Route<dynamic> route) => false,
    );
  }
  //endregion

  //region Dispose
  void dispose() {
    userProfilePictureCtrl.close();
    loadingCtrl.close();
  }
  //endregion
}
