import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:provider/provider.dart';

import 'package:swadesic/features/bottom_navigations/user_bottom_navigation/user_bottom_navigation.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/initial_onboarding_screen.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/role_selection_screen.dart';
import 'package:swadesic/features/buyers/buyer_onboarding/buyer_onboarding_new/what_is_swadesic_screen.dart';
import 'package:swadesic/features/data_model/logged_in_user_info_data_model/logged_in_user_info_data_model.dart';
import 'package:swadesic/features/data_model/product_data_model/product_data_model.dart';
import 'package:swadesic/features/external_reviews/external_review_request_screen.dart';
import 'package:swadesic/model/api_response_message.dart';
import 'package:swadesic/model/login_response/sign_in_response.dart';
import 'package:swadesic/model/user_details_response/user_details_response.dart';
import 'package:swadesic/services/get_app_data_services/get_app_data_service.dart';
import 'package:swadesic/services/signin_services/email_signin_services.dart';
import 'package:swadesic/services/signin_services/signin_services.dart';
import 'package:swadesic/services/user_detail_services/user_detail_services.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/common_methods.dart';

enum MobileOtpState { Loading, Success, Failed }

class LoginOtpBloc {
  // region Common Methods
  BuildContext context;
  final String? email;
  final String phoneNumber;
  final bool isEmailOtp;
  final bool isPhoneOtp;
  final String? googleAccessToken;
  final bool isRegisterUser;
  late SignInServices signInServices;
  late EmailSignInServices emailSignInServices;

  // endregion

  //region Controller

  final countDownCtrl = StreamController<bool>.broadcast();
  final loadingStateCtrl = StreamController<bool>.broadcast();

  //endregion

  //region Text Editing Controller
  // TextEditingController mobileNumberTextCtrl = TextEditingController();
  final TextEditingController mobileNumberOtpTextCtrl = TextEditingController();
  final TextEditingController emailOtpTextCtrl = TextEditingController();
  final KeyboardVisibilityController keyboardVisibilityController =
      KeyboardVisibilityController();

  // Loading state for verify OTP button
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  void setLoading(bool value) {
    _isLoading = value;
    loadingStateCtrl.sink.add(value);
  }

  //endregion

  //region Focus node
  final FocusNode referralFocusNode = FocusNode();

  //endregion

  // region | Constructor |
  LoginOtpBloc(this.context,
      {required this.email,
      required this.phoneNumber,
      this.googleAccessToken,
      required this.isEmailOtp,
      required this.isPhoneOtp,
      required this.isRegisterUser});

  // endregion

  // region Init
  void init() {
    signInServices = SignInServices();
    emailSignInServices = EmailSignInServices();

    // If using Google authentication, proceed with Google sign-in
    if (googleAccessToken != null && googleAccessToken!.isNotEmpty) {
      handleGoogleSignIn();
    }
  }
  // endregion

  // Handle Google sign-in
  void handleGoogleSignIn() async {
    try {
      // Create body for Google login
      Map<String, dynamic> body = {
        "email": email,
        "access_token": googleAccessToken,
      };

      // Call Google login API
      SignInResponse signInResponse =
          await emailSignInServices.googleLoginRequest(body: body);

      // Process the response
      if (!signInResponse.loginUserInfo!.profileComplete!) {
        // Save token info to global variable for temporary use
        AppConstants.appData.accessToken =
            signInResponse.loginTokenInfo!.access;
        AppConstants.appData.accessTokenExpire =
            signInResponse.loginTokenInfo!.accessTokenValidity;
        AppConstants.appData.refreshToken =
            signInResponse.loginTokenInfo!.refresh;
        AppConstants.appData.refreshExpire =
            signInResponse.loginTokenInfo!.refreshTokenValidity;

        // Go to buyer onboarding
        goToBuyerOnBoarding(
            userReference: signInResponse.loginUserInfo!.userReference!,
            icon: signInResponse.loginUserInfo!.icon);
      } else {
        // Save the login info in cache
        saveUserInfoInGlobalAndSharePref(signInResponse: signInResponse);
        // Go to buyer bottom navigation
        goToBottomNavigation();
      }
    } on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message!, context);
      }
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    }
  }
  // endregion

  // This method is no longer needed for email authentication
  //endregion

  ///1
  //region Request OTP
  void resendOtp({required Map<String, dynamic> body}) async {
    try {
      String message;

      // Use email service for email OTP
      if (isEmailOtp &&
          body.containsKey("email") &&
          body["email"] == email) {
        message = await emailSignInServices.resendOtp(body: body);
      } else {
        // Fallback to phone OTP service
        message = await signInServices.resendOtp(body: body);
      }

      // Show message
      if (context.mounted) {
        CommonMethods.toastMessage(message, context, toastShowTimer: 5);
      }
    } on ApiErrorResponseMessage catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(error.message!, context);
      }
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    }
  }
  //endregion

  ///2
  //region Verify otp
  void verifyOtp({required Map<String, dynamic> body}) async {
    try {
      // Set loading state
      setLoading(true);

      // Log the verification request
      debugPrint("Verifying OTP with body: $body");

      // Validate phone OTP if present
      if (body.containsKey('phonenumber_otp') &&
          body['phonenumber_otp'].toString().length != 6) {
        if (context.mounted) {
          CommonMethods.toastMessage("Enter a valid mobile OTP", context);
        }
        return;
      }

      // Validate email OTP if present
      if (body.containsKey('email_otp') &&
          body['email_otp'].toString().length != 6) {
        if (context.mounted) {
          CommonMethods.toastMessage("Enter a valid email OTP", context);
        }
        return;
      }

      // Call the appropriate API based on authentication type
      SignInResponse signInResponse;

      if (isEmailOtp &&
          body.containsKey('email') &&
          body.containsKey('email_otp')) {
        // Email OTP verification
        debugPrint("Using email OTP verification flow");
        signInResponse = await emailSignInServices.verifyOtp(body: body);
      } else if (isPhoneOtp &&
          body.containsKey('phonenumber') &&
          body.containsKey('phonenumber_otp')) {
        // Phone OTP verification
        debugPrint("Using phone OTP verification flow");
        signInResponse = await signInServices.verifyOtp(body: body);
      } else if (googleAccessToken != null && googleAccessToken!.isNotEmpty) {
        // Google authentication
        debugPrint("Using Google authentication flow");
        signInResponse =
            await emailSignInServices.googleLoginRequest(body: body);
      } else {
        // Fallback to standard verification
        debugPrint("Using fallback verification flow");
        signInResponse = await signInServices.verifyOtp(body: body);
      }

      // Process the response
      // Save token info to global variable for temporary use
      AppConstants.appData.accessToken = signInResponse.loginTokenInfo!.access;
      AppConstants.appData.accessTokenExpire =
          signInResponse.loginTokenInfo!.accessTokenValidity;
      AppConstants.appData.refreshToken =
          signInResponse.loginTokenInfo!.refresh;
      AppConstants.appData.refreshExpire =
          signInResponse.loginTokenInfo!.refreshTokenValidity;

      // Check if profile is complete
      if (!signInResponse.loginUserInfo!.profileComplete!) {
        // Profile is not complete, go to what is swadesic screen first
        goToWhatIsSwadesicScreen(
            userReference: signInResponse.loginUserInfo!.userReference!,
            icon: signInResponse.loginUserInfo!.icon);
      }
      // Check if user roles are empty
      else if (signInResponse.loginUserInfo!.userRoles == null ||
          signInResponse.loginUserInfo!.userRoles!.isEmpty) {
        // User roles are empty, go to role selection screen
        goToRoleSelectionScreen(
            userReference: signInResponse.loginUserInfo!.userReference!,
            icon: signInResponse.loginUserInfo!.icon);
      } else {
        // Profile is complete and user roles are set, proceed to main app
        // Save the login info in cache
        saveUserInfoInGlobalAndSharePref(signInResponse: signInResponse);
        // Go to buyer bottom navigation
        goToBottomNavigation();
      }
    } on ApiErrorResponseMessage catch (error) {
      // Reset loading state
      setLoading(false);
      if (context.mounted) {
        CommonMethods.toastMessage(error.message!, context);
      }
    } catch (error) {
      // Reset loading state
      setLoading(false);
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
    } finally {
      // Ensure loading state is always reset
      setLoading(false);
    }
  }
  //endregion

  ///Save user info in global variable and in share preference
  //region Save info in global variable and share preference
  void saveUserInfoInGlobalAndSharePref(
      {required SignInResponse signInResponse}) async {
    //Add access token
    AppConstants.appData.accessToken = signInResponse.loginTokenInfo!.access;
    //Add access token expire
    AppConstants.appData.accessTokenExpire =
        signInResponse.loginTokenInfo!.accessTokenValidity;
    //Add refresh token
    AppConstants.appData.refreshToken = signInResponse.loginTokenInfo!.refresh;
    //Add refresh token expire
    AppConstants.appData.refreshExpire =
        signInResponse.loginTokenInfo!.refreshTokenValidity;
    //Add user id
    AppConstants.appData.userId = signInResponse.loginUserInfo!.userid!;
    //Add user reference
    AppConstants.appData.userReference =
        signInResponse.loginUserInfo!.userReference!;
    //Mark user view is true
    AppConstants.appData.isUserView = true;
    //Mark store view is false
    AppConstants.appData.isStoreView = false;
    //Pin code
    AppConstants.appData.pinCode = signInResponse.loginUserInfo!.pinCode;
    //Add all data to share pref
    await AppDataService().addAppData();
  }
  //endregion

  //region Go to Bottom Navigation
  void goToBottomNavigation() async {
    await getLoggedInUserDetail();

    // Store a reference to the context to avoid using it across async gaps
    final currentContext = context;
    final isContextMounted = currentContext.mounted;

    //If non logged in user is doing sign in and app constant user reference i same as static user the pop 2 times
    if (AppConstants.isSignInScreenOpenedForStatisUser) {
      // Check if we need to navigate to the external review screen
      if (AppConstants.externalReviewParams.isNotEmpty) {
        // Get the saved parameters
        String token = AppConstants.externalReviewParams['token'] ?? '';
        String productReference =
            AppConstants.externalReviewParams['productReference'] ?? '';
        String userReference =
            AppConstants.externalReviewParams['userReference'] ?? '';

        // Clear the parameters to avoid navigation loops
        AppConstants.externalReviewParams.clear();

        // Navigate to the external review screen
        if (token.isNotEmpty &&
            productReference.isNotEmpty &&
            userReference.isNotEmpty) {
          // Use a post-frame callback to ensure navigation happens after the current frame
          WidgetsBinding.instance.addPostFrameCallback((_) {
            Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
                .popUntil((route) => route.isFirst);

            // Push the external review screen
            Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
                .push(
              MaterialPageRoute(
                builder: (context) => ExternalReviewRequestScreen(
                  token: token,
                  productReference: productReference,
                  userReference: userReference,
                ),
              ),
            );
          });
          return;
        }
      }

      // If no external review parameters, just go back to the home screen
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(AppConstants.userStoreCommonBottomNavigationContext)
            .popUntil((route) => route.isFirst);
      });
      return;
    }

    //If non static user push to UserBottomNavigation
    if (isContextMounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(currentContext).pushAndRemoveUntil(
            MaterialPageRoute(
                builder: (context) => const UserBottomNavigation()),
            (Route<dynamic> route) => false);
      });
    }
  }
  //endregion

  //region Get logged in user detail
  Future<void> getLoggedInUserDetail() async {
    late LoggedInUserInfoDataModel loggedInUserInfoDataModel =
        Provider.of<LoggedInUserInfoDataModel>(context, listen: false);
    //Get reference to Product data model
    var productDataModel =
        Provider.of<ProductDataModel>(context, listen: false);
    //region Try
    try {
      //selectedAddressRefreshCtrl.sink.add(SellerReturnWarrantyState.Loading);
      GetUserDetailsResponse userDetailsResponse = await UserDetailsServices()
          .getLoggedInUserDetail(
              userReference: AppConstants.appData.userReference!);

      ///Add user info to logged in user data model
      loggedInUserInfoDataModel.setUserInfoResponse(
          data: userDetailsResponse.userDetail!);
      //Update the buy button to refresh in all loaded product
      for (var product in productDataModel.allProducts) {
        product.isPinCodeChanged = true;
      }
      //Update ui
      productDataModel.updateUi();
    }
    //endregion
    on ApiErrorResponseMessage catch (error) {
      debugPrint(error.message);
    } catch (error) {
      debugPrint(error.toString());
    }
  }
  //endregion

  //region Go to What is Swadesic screen
  void goToWhatIsSwadesicScreen(
      {required String userReference, required String? icon}) {
    // First, we need to get basic user data to pass to the what is swadesic screen
    getUserBasicInfo(userReference).then((userData) {
      if (userData != null && context.mounted) {
        var screen = WhatIsSwadesicScreen(
          userReference: userReference,
          userData: userData,
          icon: icon,
        );
        var route = MaterialPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      }
    });
  }
  //endregion

  //region Go to Buyer on boarding screen
  void goToBuyerOnBoarding(
      {required String userReference, required String? icon}) {
    var screen = InitialOnboardingScreen(
      userReference: userReference,
      icon: icon,
    );
    var route = MaterialPageRoute(builder: (context) => screen);
    Navigator.push(context, route);
  }
  //endregion

  //region Go to Role Selection screen
  void goToRoleSelectionScreen(
      {required String userReference, required String? icon}) {
    // First, we need to get basic user data to pass to the role selection screen
    getUserBasicInfo(userReference).then((userData) {
      if (userData != null && context.mounted) {
        var screen = RoleSelectionScreen(
          userReference: userReference,
          userData: userData,
          icon: icon,
        );
        var route = MaterialPageRoute(builder: (context) => screen);
        Navigator.push(context, route);
      }
    });
  }

  // Get basic user info for role selection screen
  Future<Map<String, dynamic>?> getUserBasicInfo(String userReference) async {
    try {
      GetUserDetailsResponse userDetailsResponse = await UserDetailsServices()
          .getUserDetail(userResponse: userReference);

      // Extract basic user data needed for role selection
      return {
        "user_reference": userReference,
        "user_name": userDetailsResponse.userDetail?.userName ?? "",
        "first_name": userDetailsResponse.userDetail?.firstName ?? "",
        "display_name": userDetailsResponse.userDetail?.displayName ?? "",
        "gender": userDetailsResponse.userDetail?.gender ?? "",
        "user_location": userDetailsResponse.userDetail?.userLocation ?? "",
      };
    } catch (error) {
      if (context.mounted) {
        CommonMethods.toastMessage(AppStrings.commonErrorMessage, context);
      }
      return null;
    }
  }
  //endregion

//region Dispose
  void dispose() {
    countDownCtrl.close();
    loadingStateCtrl.close();
  }
//endregion
}
